# 普通按键

## 初始提示词

- 1。阅读‘普通按键路径.md’并用于创建文件
- 2.阅读‘前端技术栈.md’并用于构建代码
- 3.严格执行‘普通按键.md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.更新.gitignore文档

## 优化提示词

请按照以下步骤严格执行普通按键功能的构建任务：

1. **文件路径规划**：首先阅读 `普通按键路径.md` 文档，了解普通按键相关文件的存储路径和文件结构规范，并严格按照该文档中的路径要求创建所需文件
2. **技术栈确认**：阅读 `前端技术栈.md` 文档，确认当前项目使用的前端技术栈（React、TypeScript、状态管理等），并严格按照该技术栈规范进行代码构建
3. **功能实现**：严格按照 `普通按键.md` 文档中的具体指示和要求实现普通按键功能，不得偏离文档中的规格说明
4. **文档阅读限制**：在执行过程中，禁止提前阅读除上述三个指定文档以外的任何其他文档，避免功能范围扩散
5. **范围控制**：仅完成本次任务中明确要求的普通按键逻辑流程，禁止添加额外功能或进行功能补全
6. **项目配置更新**：完成功能实现后，更新项目根目录下的 `.gitignore` 文件，确保新增的文件类型得到正确的版本控制管理
7. **功能测试**：实现完成后，编写并执行相应的测试用例，验证普通按键功能的正确性
注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步。
