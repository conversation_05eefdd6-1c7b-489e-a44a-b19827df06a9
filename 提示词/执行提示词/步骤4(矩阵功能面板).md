# 矩阵功能面板

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.严格执行‘三级容器(矩阵).md’中的指示
- 3.禁止提前阅读除提示词描述以外的文档
- 4.仅完成该文本提供的逻辑流程，禁止补全
- 5.检测代码能否正常运行
- 6.更新.gitignore文档

## 优化提示词

请按照以下步骤严格执行矩阵功能面板的构建任务：

1. **技术栈准备**：首先阅读项目根目录下的'前端技术栈.md'文档，理解并应用其中定义的技术栈要求来构建代码
2. **架构实现**：严格按照'三级容器(矩阵).md'文档中的具体指示和架构要求进行开发，不得偏离文档中的设计规范
3. **文档访问限制**：在执行过程中，除了步骤1和步骤2中明确指定的文档外，禁止主动阅读或参考其他项目文档，专注于当前任务范围
4. **功能范围控制**：严格按照本提示词中定义的逻辑流程进行开发，不得自行添加额外功能或进行功能扩展
5. **代码质量验证**：完成代码编写后，必须进行功能测试，确保代码能够正常运行且符合预期功能要求
6. **项目配置**：创建或更新项目根目录下的.gitignore文件，确保包含适当的忽略规则以维护代码仓库的整洁性
7. **项目配置**：根据项目的技术栈和依赖情况，创建或更新 `.gitignore` 文件，确保忽略不必要的文件（如 node_modules、构建产物、IDE配置文件等）
请在每个步骤完成后进行确认，并在遇到技术问题时及时报告。
